<script setup lang="ts">
import { computed } from "vue"

import DeckModal from "~/components/app/deck-modal.vue"
import DecksCarousel from "~/components/app/decks-carousel.vue"
import decksDataJson from "~/data/decks.json"

const { isModalOpen, selectedDeck, openModal, closeModal } = useModal()

const decksData = decksDataJson.map(deck => ({
  ...deck,
  action: () => {
    if (deck.isCompleted) {
      openModal(deck)
    }
    else {
      console.log(`${deck.category} clicked`)
    }
  },
}))

// Filter decks into two categories
const todayDecks = computed(() => decksData.filter(deck => !deck.isCompleted))
const completedDecks = computed(() => decksData.filter(deck => deck.isCompleted))
</script>

<template>
  <div class="home">
    <AppHeartButton />
    <section class="decks">
      <h2>Today</h2>
      <DecksCarousel
        :decks="todayDecks"
        :show-navigation="true"
        :scroll-amount="350"
      />
    </section>
    <section class="decks">
      <h3>Completed</h3>
      <DecksCarousel
        :decks="completedDecks"
        :show-navigation="true"
        :scroll-amount="350"
      />
    </section>

    <!-- Modal -->
    <DeckModal
      :is-open="isModalOpen"
      :deck="selectedDeck"
      @close="closeModal"
    />
  </div>
</template>

<style scoped lang="scss">
  .home {
  background: url("/assets/images/background.svg") no-repeat;
  background-size: calc(100% - 2rem);
  background-position-x: center;
  background-position-y: 76px;
  background-color: var(--purple);
  height: 100%;
  width: 100%;
  padding: 130px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 16px;

  .decks {
    width: 100%;
    max-width: 100%;

    h2,
    h3 {
      text-align: center;
    }
  }
}
</style>
