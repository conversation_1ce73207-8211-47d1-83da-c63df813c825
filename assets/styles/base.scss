/* Modern CSS Reset */
*,
*::before,
*::after {
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
}

/* Remove default margin and padding */
body,
h1,
h2,
h3,
h4,
h5,
h6,
p,
figure,
blockquote,
dl,
dd {
  margin: 0;
}

/* Set core body defaults */
body {
  min-height: 100vh;
  scroll-behavior: smooth;
  text-rendering: optimizeSpeed;
  line-height: 1.5;
  font-family: var(--font-secondary);
  color: var(--color-text-primary);
  background-color: var(--purple);
}

/* Remove list styles on ul, ol elements */
ul,
ol {
  list-style: none;
  padding: 0;
  margin: 0;
}

/* Make images easier to work with */
img,
picture {
  max-width: 100%;
  display: block;
}

/* Inherit fonts for inputs and buttons */
input,
button,
textarea,
select {
  font: inherit;
}

/* Set default focus styles */
:focus {
  outline: none;
  border: none;
}

/* Remove default button styles */
button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0;
}

/* Set default link styles */
a {
  color: var(--color-primary);
  text-decoration: none;
}

a:hover {
  text-decoration: underline;
}

/* Set default table styles */
table {
  border-collapse: collapse;
  width: 100%;
}

th,
td {
  padding: 0.5rem;
  text-align: left;
}

/* Set default form element styles */
input,
textarea,
select {
  padding: 0.5rem;
  border: 1px solid var(--color-gray-400);
  border-radius: var(--border-radius-sm);
}

input:focus,
textarea:focus,
select:focus {
  border-color: var(--color-primary);
}

h2,
h3 {
  font-family: var(--font-primary);
  -webkit-font-feature-settings: "ss01";
  -moz-font-feature-settings: "ss01";
  font-feature-settings: "ss01";
  text-rendering: optimizeSpeed;
  font-weight: 900;
  font-variation-settings: "wght" 900;
  letter-spacing: -0.03em;
}

h2 {
  font-size: 36px;
  line-height: 42px;
}

h3 {
  font-size: 24px;
  line-height: 32px;
}
