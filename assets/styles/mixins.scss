@mixin font-primary {
  font-family: var(--font-primary);
  -webkit-font-feature-settings: "ss01";
  -moz-font-feature-settings: "ss01";
  font-feature-settings: "ss01";
  text-rendering: optimizeSpeed;
  font-weight: 900;
  font-variation-settings: "wght" 900;
  letter-spacing: -0.03em;
}

@mixin font-secondary {
  font-family: var(--font-secondary);
  -webkit-font-feature-settings: "cv11";
  -moz-font-feature-settings: "cv11";
  font-feature-settings: "cv11";
  font-weight: 500;
  font-variation-settings: "wght" 500;
}
