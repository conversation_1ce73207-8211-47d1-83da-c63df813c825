<svg width="359" height="312" viewBox="0 0 359 312" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M243.681 240.418C243.681 272.262 269.496 298.078 301.34 298.078C333.184 298.078 359 272.262 359 240.418C359 208.573 333.184 182.758 301.34 182.758C269.496 182.758 243.681 208.573 243.681 240.418Z" fill="url(#paint0_linear_0_1)"/>
<path d="M243.681 240.418C243.681 272.262 269.496 298.078 301.34 298.078C305.61 298.078 309.768 297.609 313.774 296.728C312.464 296.818 311.143 296.867 309.811 296.867C277.967 296.867 252.152 271.052 252.152 239.208C252.152 211.631 271.512 188.581 297.379 182.896C267.383 184.932 243.681 209.905 243.681 240.418Z" fill="url(#paint1_linear_0_1)"/>
<path d="M233.343 131.31C233.343 144.736 244.228 155.621 257.655 155.621C271.08 155.621 281.964 144.736 281.964 131.31C281.964 117.883 271.08 107 257.655 107C244.228 107 233.343 117.883 233.343 131.31Z" fill="url(#paint2_linear_0_1)"/>
<path d="M287.443 249.437C287.443 255.866 282.23 261.079 275.801 261.079C269.371 261.079 264.16 255.866 264.16 249.437C264.16 243.007 269.371 237.795 275.801 237.795C282.23 237.795 287.443 243.007 287.443 249.437Z" stroke="#00005A" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M338.118 260.394C338.118 270.038 330.3 277.856 320.656 277.856C311.012 277.856 303.193 270.038 303.193 260.394C303.193 250.749 311.012 242.932 320.656 242.932C330.3 242.932 338.118 250.749 338.118 260.394Z" stroke="#00005A" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M282.337 133.33C288.426 138.703 291.479 143.792 289.893 147.043C287.043 152.882 270.299 150.572 252.494 141.883C234.69 133.194 222.566 121.415 225.415 115.577C227.033 112.263 233.126 111.574 241.349 113.186" stroke="#24FBD5" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
<mask id="mask0_0_1" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="243" y="182" width="116" height="117">
<path d="M301.34 297.328C332.77 297.328 358.25 271.848 358.25 240.418C358.25 208.988 332.77 183.508 301.34 183.508C269.911 183.508 244.431 208.988 244.431 240.418C244.431 271.847 269.911 297.328 301.34 297.328Z" fill="url(#paint3_linear_0_1)" stroke="#642FC3" stroke-width="1.5"/>
</mask>
<g mask="url(#mask0_0_1)">
<path d="M381.26 191.913C381.26 215.93 361.792 235.398 337.776 235.398C313.76 235.398 294.291 215.93 294.291 191.913C294.291 167.897 313.76 148.429 337.776 148.429C361.792 148.429 381.26 167.897 381.26 191.913Z" stroke="#00005A" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M295.661 293.949C295.661 303.026 288.302 310.384 279.225 310.384C270.149 310.384 262.79 303.026 262.79 293.949C262.79 284.872 270.149 277.514 279.225 277.514C288.302 277.514 295.661 284.872 295.661 293.949Z" stroke="#00005A" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M276.829 200.131C276.829 219.42 261.192 235.056 241.904 235.056C222.615 235.056 206.979 219.42 206.979 200.131C206.979 180.843 222.615 165.207 241.904 165.207C261.192 165.207 276.829 180.843 276.829 200.131Z" stroke="#00005A" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
</g>
<g clip-path="url(#paint4_diamond_0_1_clip_path)" data-figma-skip-parse="true"><g transform="matrix(0.0135069 0 0 0.0135069 13.5068 21.6512)"><rect x="0" y="0" width="1074.04" height="1074.04" fill="url(#paint4_diamond_0_1)" opacity="1" shape-rendering="crispEdges"/><rect x="0" y="0" width="1074.04" height="1074.04" transform="scale(1 -1)" fill="url(#paint4_diamond_0_1)" opacity="1" shape-rendering="crispEdges"/><rect x="0" y="0" width="1074.04" height="1074.04" transform="scale(-1 1)" fill="url(#paint4_diamond_0_1)" opacity="1" shape-rendering="crispEdges"/><rect x="0" y="0" width="1074.04" height="1074.04" transform="scale(-1)" fill="url(#paint4_diamond_0_1)" opacity="1" shape-rendering="crispEdges"/></g></g><path d="M0 21.6512C9.80703 23.6943 11.4636 25.3505 13.5067 35.1582C15.5501 25.3505 17.206 23.6943 27.0137 21.6512C17.206 19.6081 15.5501 17.952 13.5067 8.14426C11.4636 17.952 9.80703 19.6081 0 21.6512Z" data-figma-gradient-fill="{&#34;type&#34;:&#34;GRADIENT_DIAMOND&#34;,&#34;stops&#34;:[{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:0.90588235855102539,&#34;b&#34;:0.0,&#34;a&#34;:1.0},&#34;position&#34;:0.0},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:0.64705884456634521,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:1.0}],&#34;stopsVar&#34;:[],&#34;transform&#34;:{&#34;m00&#34;:27.013772964477539,&#34;m01&#34;:0.0,&#34;m02&#34;:-9.1790148871950805e-05,&#34;m10&#34;:0.0,&#34;m11&#34;:27.013874053955078,&#34;m12&#34;:8.1442813873291016},&#34;opacity&#34;:1.0,&#34;blendMode&#34;:&#34;NORMAL&#34;,&#34;visible&#34;:true}"/>
<g clip-path="url(#paint5_diamond_0_1_clip_path)" data-figma-skip-parse="true"><g transform="matrix(0.00836423 0 0 0.00836421 184.427 59.8848)"><rect x="0" y="0" width="1119.56" height="1119.56" fill="url(#paint5_diamond_0_1)" opacity="1" shape-rendering="crispEdges"/><rect x="0" y="0" width="1119.56" height="1119.56" transform="scale(1 -1)" fill="url(#paint5_diamond_0_1)" opacity="1" shape-rendering="crispEdges"/><rect x="0" y="0" width="1119.56" height="1119.56" transform="scale(-1 1)" fill="url(#paint5_diamond_0_1)" opacity="1" shape-rendering="crispEdges"/><rect x="0" y="0" width="1119.56" height="1119.56" transform="scale(-1)" fill="url(#paint5_diamond_0_1)" opacity="1" shape-rendering="crispEdges"/></g></g><path d="M176.063 59.8847C182.136 61.1499 183.162 62.1757 184.427 68.249C185.692 62.1757 186.718 61.1499 192.791 59.8847C186.718 58.6195 185.692 57.5939 184.427 51.5206C183.162 57.5939 182.136 58.6195 176.063 59.8847Z" data-figma-gradient-fill="{&#34;type&#34;:&#34;GRADIENT_DIAMOND&#34;,&#34;stops&#34;:[{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:0.90588235855102539,&#34;b&#34;:0.0,&#34;a&#34;:1.0},&#34;position&#34;:0.0},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:0.64705884456634521,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:1.0}],&#34;stopsVar&#34;:[],&#34;transform&#34;:{&#34;m00&#34;:16.728462219238281,&#34;m01&#34;:0.0,&#34;m02&#34;:176.06256103515625,&#34;m10&#34;:0.0,&#34;m11&#34;:16.728429794311523,&#34;m12&#34;:51.520565032958984},&#34;opacity&#34;:1.0,&#34;blendMode&#34;:&#34;NORMAL&#34;,&#34;visible&#34;:true}"/>
<g clip-path="url(#paint6_diamond_0_1_clip_path)" data-figma-skip-parse="true"><g transform="matrix(0.0203749 0 0 0.0203747 113.344 117.628)"><rect x="0" y="0" width="1049.08" height="1049.08" fill="url(#paint6_diamond_0_1)" opacity="1" shape-rendering="crispEdges"/><rect x="0" y="0" width="1049.08" height="1049.08" transform="scale(1 -1)" fill="url(#paint6_diamond_0_1)" opacity="1" shape-rendering="crispEdges"/><rect x="0" y="0" width="1049.08" height="1049.08" transform="scale(-1 1)" fill="url(#paint6_diamond_0_1)" opacity="1" shape-rendering="crispEdges"/><rect x="0" y="0" width="1049.08" height="1049.08" transform="scale(-1)" fill="url(#paint6_diamond_0_1)" opacity="1" shape-rendering="crispEdges"/></g></g><path d="M92.9688 117.628C107.764 120.71 110.262 123.209 113.344 138.003C116.426 123.209 118.924 120.71 133.719 117.628C118.924 114.546 116.426 112.048 113.344 97.2533C110.262 112.048 107.764 114.546 92.9688 117.628Z" data-figma-gradient-fill="{&#34;type&#34;:&#34;GRADIENT_DIAMOND&#34;,&#34;stops&#34;:[{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:0.90588235855102539,&#34;b&#34;:0.0,&#34;a&#34;:1.0},&#34;position&#34;:0.0},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:0.64705884456634521,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:1.0}],&#34;stopsVar&#34;:[],&#34;transform&#34;:{&#34;m00&#34;:40.749885559082031,&#34;m01&#34;:0.0,&#34;m02&#34;:92.968612670898438,&#34;m10&#34;:0.0,&#34;m11&#34;:40.749481201171875,&#34;m12&#34;:97.253372192382812},&#34;opacity&#34;:1.0,&#34;blendMode&#34;:&#34;NORMAL&#34;,&#34;visible&#34;:true}"/>
<path d="M115.323 15.7429C118.428 15.7429 120.945 13.2258 120.945 10.1208C120.945 7.01669 118.428 4.49969 115.323 4.49969C112.219 4.49982 109.702 7.01677 109.702 10.1208C109.702 13.2257 112.219 15.7427 115.323 15.7429Z" fill="url(#paint7_linear_0_1)" stroke="#00005A"/>
<path d="M115.323 15.7429C115.723 15.7429 116.113 15.7012 116.489 15.6218C113.356 15.0979 110.968 12.3742 110.968 9.09247C110.968 7.82192 111.328 6.6362 111.948 5.6286C110.585 6.65394 109.702 8.28417 109.702 10.1208C109.702 13.2257 112.219 15.7427 115.323 15.7429Z" fill="url(#paint8_linear_0_1)" stroke="#00005A"/>
<path d="M121.445 10.1216C121.445 13.5025 118.704 16.2432 115.323 16.2432C111.943 16.2432 109.202 13.5025 109.202 10.1216C109.202 6.74094 111.943 4.0003 115.323 4.0003C118.704 4.0003 121.445 6.74094 121.445 10.1216Z" stroke="#00005A" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
<mask id="path-17-inside-1_0_1" fill="white">
<path d="M70.1788 54.3985C70.1788 64.1185 78.0582 71.9977 87.7775 71.9977C97.4976 71.9977 105.377 64.1185 105.377 54.3985C105.377 44.6792 97.4976 36.7998 87.7775 36.7998C78.0582 36.7998 70.1788 44.6792 70.1788 54.3985Z"/>
</mask>
<path d="M70.1788 54.3985C70.1788 64.1185 78.0582 71.9977 87.7775 71.9977C97.4976 71.9977 105.377 64.1185 105.377 54.3985C105.377 44.6792 97.4976 36.7998 87.7775 36.7998C78.0582 36.7998 70.1788 44.6792 70.1788 54.3985Z" fill="url(#paint9_linear_0_1)"/>
<path d="M71.1788 54.3985C71.1788 63.5663 78.6105 70.9977 87.7775 70.9977V72.9977C77.5059 72.9977 69.1788 64.6708 69.1788 54.3985H71.1788ZM87.7775 70.9977C96.9453 70.9977 104.377 63.5663 104.377 54.3985H106.377C106.377 64.6708 98.0498 72.9977 87.7775 72.9977V70.9977ZM104.377 54.3985C104.377 45.2315 96.9453 37.7998 87.7775 37.7998V35.7998C98.0498 35.7998 106.377 44.1269 106.377 54.3985H104.377ZM87.7775 37.7998C78.6105 37.7998 71.1788 45.2315 71.1788 54.3985H69.1788C69.1788 44.1269 77.5059 35.7998 87.7775 35.7998V37.7998Z" fill="#00005A" mask="url(#path-17-inside-1_0_1)"/>
<mask id="path-19-inside-2_0_1" fill="white">
<path d="M70.1788 54.3985C70.1788 64.1185 78.0582 71.9977 87.7775 71.9977C89.8482 71.9977 91.8338 71.6377 93.6789 70.9808C92.8281 71.1069 91.9581 71.1741 91.0725 71.1741C81.353 71.1741 73.4738 63.2947 73.4738 53.5747C73.4738 45.926 78.3553 39.4191 85.1719 36.9932C76.688 38.2524 70.1788 45.5648 70.1788 54.3985Z"/>
</mask>
<path d="M70.1788 54.3985C70.1788 64.1185 78.0582 71.9977 87.7775 71.9977C89.8482 71.9977 91.8338 71.6377 93.6789 70.9808C92.8281 71.1069 91.9581 71.1741 91.0725 71.1741C81.353 71.1741 73.4738 63.2947 73.4738 53.5747C73.4738 45.926 78.3553 39.4191 85.1719 36.9932C76.688 38.2524 70.1788 45.5648 70.1788 54.3985Z" fill="url(#paint10_linear_0_1)"/>
<path d="M93.6789 70.9808L93.5322 69.9916L94.0143 71.9228L93.6789 70.9808ZM85.1719 36.9932L85.0251 36.0041L85.5072 37.9353L85.1719 36.9932ZM71.1788 54.3985C71.1788 63.5663 78.6105 70.9977 87.7775 70.9977V72.9977C77.5059 72.9977 69.1788 64.6708 69.1788 54.3985H71.1788ZM87.7775 70.9977C89.7314 70.9977 91.6037 70.6581 93.3435 70.0387L94.0143 71.9228C92.0639 72.6173 89.9649 72.9977 87.7775 72.9977V70.9977ZM93.8255 71.97C92.9278 72.103 92.0088 72.1741 91.0725 72.1741V70.1741C91.9074 70.1741 92.7285 70.1107 93.5322 69.9916L93.8255 71.97ZM91.0725 72.1741C80.8007 72.1741 72.4738 63.847 72.4738 53.5747H74.4738C74.4738 62.7424 81.9053 70.1741 91.0725 70.1741V72.1741ZM72.4738 53.5747C72.4738 45.4895 77.6343 38.6142 84.8366 36.0511L85.5072 37.9353C79.0762 40.2239 74.4738 46.3625 74.4738 53.5747H72.4738ZM85.3187 37.9824C77.3177 39.1699 71.1788 46.0678 71.1788 54.3985H69.1788C69.1788 45.0618 76.0583 37.3349 85.0251 36.0041L85.3187 37.9824Z" fill="#00005A" mask="url(#path-19-inside-2_0_1)"/>
<path d="M105.377 54.3987C105.377 64.1183 97.4974 71.9977 87.7778 71.9977C78.058 71.9977 70.1789 64.1183 70.1789 54.3987C70.1789 44.679 78.058 36.7998 87.7778 36.7998C97.4974 36.7998 105.377 44.679 105.377 54.3987Z" stroke="#00005A" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M71.0969 49.0616L96.3712 39.0142" stroke="#00005A" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M100.204 42.0178L73.5831 64.3916" stroke="#00005A" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M77.8292 68.8457L105.175 56.6228" stroke="#00005A" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
<g filter="url(#filter0_i_0_1)">
<circle cx="75.4991" cy="237.378" r="17.6028" transform="rotate(90 75.4991 237.378)" fill="url(#paint11_linear_0_1)"/>
</g>
<circle cx="75.4991" cy="237.378" r="16.8528" transform="rotate(90 75.4991 237.378)" stroke="#5D0281" stroke-width="1.5"/>
<g style="mix-blend-mode:multiply">
<path d="M71.6971 254.569C67.9163 253.737 64.5915 251.689 62.153 248.856C64.2491 246.071 71.2507 242.863 73.0479 247.782C73.8286 249.919 72.6703 252.676 71.6971 254.569Z" fill="#D9D9D9"/>
<path d="M71.6971 254.569C67.9163 253.737 64.5915 251.689 62.153 248.856C64.2491 246.071 71.2507 242.863 73.0479 247.782C73.8286 249.919 72.6703 252.676 71.6971 254.569Z" fill="url(#paint12_linear_0_1)"/>
<path d="M85.3227 235.96C81.0575 241.666 76.3151 229.496 72.7289 228.207C66.8394 226.09 64.46 234.358 68.1026 237.888C70.6 240.308 76.2843 240.779 79.0044 243.499C82.5584 247.053 78.4097 252.633 85.7438 251.694C90.1991 248.5 93.1019 243.278 93.1019 237.378C93.1019 233.011 91.512 229.016 88.8794 225.939C87.1188 229.022 87.4616 233.099 85.3227 235.96Z" fill="url(#paint13_linear_0_1)"/>
</g>
<g clip-path="url(#paint14_diamond_0_1_clip_path)" data-figma-skip-parse="true"><g transform="matrix(-6.23874e-10 0.0142726 0.0141379 6.17985e-10 68.3634 186.472)"><rect x="0" y="0" width="1105.1" height="1106.1" fill="url(#paint14_diamond_0_1)" opacity="1" shape-rendering="crispEdges"/><rect x="0" y="0" width="1105.1" height="1106.1" transform="scale(1 -1)" fill="url(#paint14_diamond_0_1)" opacity="1" shape-rendering="crispEdges"/><rect x="0" y="0" width="1105.1" height="1106.1" transform="scale(-1 1)" fill="url(#paint14_diamond_0_1)" opacity="1" shape-rendering="crispEdges"/><rect x="0" y="0" width="1105.1" height="1106.1" transform="scale(-1)" fill="url(#paint14_diamond_0_1)" opacity="1" shape-rendering="crispEdges"/></g></g><path d="M68.7519 175.038L68.3635 172.2L67.975 175.038C67.1728 180.901 62.5133 185.481 56.6384 186.184L54.2256 186.472L56.6384 186.761C62.5133 187.463 67.1728 192.044 67.975 197.906L68.3635 200.745L68.7519 197.906C69.5541 192.044 74.2136 187.463 80.0885 186.761L82.5013 186.472L80.0885 186.184C74.2136 185.481 69.5541 180.901 68.7519 175.038Z" data-figma-gradient-fill="{&#34;type&#34;:&#34;GRADIENT_DIAMOND&#34;,&#34;stops&#34;:[{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:0.46666666865348816,&#34;b&#34;:0.67843139171600342,&#34;a&#34;:1.0},&#34;position&#34;:0.0},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:0.64705884456634521,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:1.0}],&#34;stopsVar&#34;:[],&#34;transform&#34;:{&#34;m00&#34;:-1.2477480595407542e-06,&#34;m01&#34;:28.275709152221680,&#34;m02&#34;:54.225582122802734,&#34;m10&#34;:28.545146942138672,&#34;m11&#34;:1.2359704442133079e-06,&#34;m12&#34;:172.19979858398438},&#34;opacity&#34;:1.0,&#34;blendMode&#34;:&#34;NORMAL&#34;,&#34;visible&#34;:true}"/>
<g clip-path="url(#paint15_diamond_0_1_clip_path)" data-figma-skip-parse="true"><g transform="matrix(-3.7814e-10 0.00865082 0.00856917 3.7457e-10 23.3179 156.651)"><rect x="0" y="0" width="1173.4" height="1175.05" fill="url(#paint15_diamond_0_1)" opacity="1" shape-rendering="crispEdges"/><rect x="0" y="0" width="1173.4" height="1175.05" transform="scale(1 -1)" fill="url(#paint15_diamond_0_1)" opacity="1" shape-rendering="crispEdges"/><rect x="0" y="0" width="1173.4" height="1175.05" transform="scale(-1 1)" fill="url(#paint15_diamond_0_1)" opacity="1" shape-rendering="crispEdges"/><rect x="0" y="0" width="1173.4" height="1175.05" transform="scale(-1)" fill="url(#paint15_diamond_0_1)" opacity="1" shape-rendering="crispEdges"/></g></g><path d="M24.3912 151.628L23.3179 148L22.2445 151.628C21.66 153.603 20.1027 155.141 18.1198 155.7L14.7487 156.651L18.1198 157.602C20.1027 158.161 21.66 159.698 22.2445 161.674L23.3179 165.302L24.3912 161.674C24.9758 159.698 26.5331 158.161 28.516 157.602L31.8871 156.651L28.516 155.7C26.5331 155.141 24.9758 153.603 24.3912 151.628Z" data-figma-gradient-fill="{&#34;type&#34;:&#34;GRADIENT_DIAMOND&#34;,&#34;stops&#34;:[{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:0.46666666865348816,&#34;b&#34;:0.67843139171600342,&#34;a&#34;:1.0},&#34;position&#34;:0.0},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:0.64705884456634521,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:1.0}],&#34;stopsVar&#34;:[],&#34;transform&#34;:{&#34;m00&#34;:-7.5627900741892518e-07,&#34;m01&#34;:17.138338088989258,&#34;m02&#34;:14.748717308044434,&#34;m10&#34;:17.301647186279297,&#34;m11&#34;:7.4914049719154718e-07,&#34;m12&#34;:147.99993896484375},&#34;opacity&#34;:1.0,&#34;blendMode&#34;:&#34;NORMAL&#34;,&#34;visible&#34;:true}"/>
<g clip-path="url(#paint16_diamond_0_1_clip_path)" data-figma-skip-parse="true"><g transform="matrix(-6.23874e-10 0.0142726 0.0141379 6.17985e-10 30.3029 232.144)"><rect x="0" y="0" width="1105.1" height="1106.1" fill="url(#paint16_diamond_0_1)" opacity="1" shape-rendering="crispEdges"/><rect x="0" y="0" width="1105.1" height="1106.1" transform="scale(1 -1)" fill="url(#paint16_diamond_0_1)" opacity="1" shape-rendering="crispEdges"/><rect x="0" y="0" width="1105.1" height="1106.1" transform="scale(-1 1)" fill="url(#paint16_diamond_0_1)" opacity="1" shape-rendering="crispEdges"/><rect x="0" y="0" width="1105.1" height="1106.1" transform="scale(-1)" fill="url(#paint16_diamond_0_1)" opacity="1" shape-rendering="crispEdges"/></g></g><path d="M30.6913 220.71L30.3029 217.872L29.9145 220.71C29.1123 226.573 24.4528 231.154 18.5779 231.856L16.165 232.144L18.5779 232.433C24.4528 233.135 29.1123 237.716 29.9145 243.579L30.3029 246.417L30.6913 243.579C31.4935 237.716 36.153 233.135 42.0279 232.433L44.4408 232.144L42.0279 231.856C36.153 231.154 31.4935 226.573 30.6913 220.71Z" data-figma-gradient-fill="{&#34;type&#34;:&#34;GRADIENT_DIAMOND&#34;,&#34;stops&#34;:[{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:0.46666666865348816,&#34;b&#34;:0.67843139171600342,&#34;a&#34;:1.0},&#34;position&#34;:0.0},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:0.64705884456634521,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:1.0}],&#34;stopsVar&#34;:[],&#34;transform&#34;:{&#34;m00&#34;:-1.2477480595407542e-06,&#34;m01&#34;:28.275709152221680,&#34;m02&#34;:16.165037155151367,&#34;m10&#34;:28.545146942138672,&#34;m11&#34;:1.2359704442133079e-06,&#34;m12&#34;:217.87185668945312},&#34;opacity&#34;:1.0,&#34;blendMode&#34;:&#34;NORMAL&#34;,&#34;visible&#34;:true}"/>
<g clip-path="url(#paint17_diamond_0_1_clip_path)" data-figma-skip-parse="true"><g transform="matrix(-6.23874e-10 0.0142726 0.0141379 6.17985e-10 103.557 276.865)"><rect x="0" y="0" width="1105.1" height="1106.1" fill="url(#paint17_diamond_0_1)" opacity="1" shape-rendering="crispEdges"/><rect x="0" y="0" width="1105.1" height="1106.1" transform="scale(1 -1)" fill="url(#paint17_diamond_0_1)" opacity="1" shape-rendering="crispEdges"/><rect x="0" y="0" width="1105.1" height="1106.1" transform="scale(-1 1)" fill="url(#paint17_diamond_0_1)" opacity="1" shape-rendering="crispEdges"/><rect x="0" y="0" width="1105.1" height="1106.1" transform="scale(-1)" fill="url(#paint17_diamond_0_1)" opacity="1" shape-rendering="crispEdges"/></g></g><path d="M103.945 265.431L103.557 262.593L103.168 265.431C102.366 271.293 97.7067 275.874 91.8318 276.577L89.4189 276.865L91.8318 277.154C97.7067 277.856 102.366 282.437 103.168 288.299L103.557 291.138L103.945 288.299C104.747 282.437 109.407 277.856 115.282 277.154L117.695 276.865L115.282 276.577C109.407 275.874 104.747 271.293 103.945 265.431Z" data-figma-gradient-fill="{&#34;type&#34;:&#34;GRADIENT_DIAMOND&#34;,&#34;stops&#34;:[{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:0.46666666865348816,&#34;b&#34;:0.67843139171600342,&#34;a&#34;:1.0},&#34;position&#34;:0.0},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:0.64705884456634521,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:1.0}],&#34;stopsVar&#34;:[],&#34;transform&#34;:{&#34;m00&#34;:-1.2477480595407542e-06,&#34;m01&#34;:28.275709152221680,&#34;m02&#34;:89.41894531250,&#34;m10&#34;:28.545146942138672,&#34;m11&#34;:1.2359704442133079e-06,&#34;m12&#34;:262.59259033203125},&#34;opacity&#34;:1.0,&#34;blendMode&#34;:&#34;NORMAL&#34;,&#34;visible&#34;:true}"/>
<circle cx="101.906" cy="226.405" r="1.903" transform="rotate(90 101.906 226.405)" fill="#3FD5FF"/>
<circle cx="6.90303" cy="164.742" r="1.903" transform="rotate(90 6.90303 164.742)" fill="#3FD5FF"/>
<circle cx="32.2448" cy="191.337" r="1.903" transform="rotate(90 32.2448 191.337)" fill="#3FD5FF"/>
<circle cx="113.471" cy="309.371" r="1.903" transform="rotate(90 113.471 309.371)" fill="#3FD5FF"/>
<circle cx="72.5563" cy="292.244" r="1.903" transform="rotate(90 72.5563 292.244)" fill="#3FD5FF"/>
<circle cx="75.4991" cy="237.378" r="16.8528" transform="rotate(90 75.4991 237.378)" stroke="#5D0281" stroke-width="1.5"/>
<circle cx="75.4991" cy="237.378" r="16.8528" transform="rotate(90 75.4991 237.378)" stroke="#5D0281" stroke-width="1.5"/>
<path d="M281.651 43.5646C285.365 49.5008 292.823 51.8549 299.254 49.1682L299.559 49.0363L333.696 33.8439L323.559 30.3928L323.018 30.2092L323.296 29.7101L332.611 13.0353L312.8 13.7248L312.141 13.7482L312.327 13.1154L315.531 2.22089L285.287 24.4963C279.37 28.8552 277.742 36.9771 281.47 43.2668L281.651 43.5646Z" fill="url(#paint18_linear_0_1)" stroke="#00005A" stroke-width="0.951812"/>
<path d="M294.49 50.2521C289.959 49.8679 285.689 47.4627 283.05 43.4533L282.858 43.1545H282.857C279.473 37.744 279.887 30.9761 283.485 26.0705C279.075 30.6266 278.153 37.6716 281.47 43.2668L281.651 43.5646C284.47 48.0697 289.444 50.5111 294.49 50.2521Z" fill="url(#paint19_linear_0_1)" stroke="#00005A" stroke-width="0.951812"/>
<path d="M293.885 41.7207L294.146 41.5273L294.422 41.7011L297.844 43.8623L302.463 39.8857L299.932 34.205L299.831 33.9775L299.96 33.7646L302.124 30.1972L294.483 25.6845L286.421 30.0254L284.183 38.9121L289.473 44.9834L293.885 41.7207Z" fill="#8171FF" stroke="#00005A" stroke-width="0.951812"/>
<path d="M300.189 42.2282L297.487 40.3073L295.132 42.0622L297.872 43.9011L300.189 42.2282Z" fill="url(#paint20_linear_0_1)" stroke="#00005A" stroke-width="0.951812"/>
<path d="M292.007 43.3727L286.732 37.317L286.569 37.1295L286.629 36.8883L288.65 28.86L286.419 30.0289L284.183 38.9127L289.491 45.0045L292.007 43.3727Z" fill="url(#paint21_linear_0_1)" stroke="#00005A" stroke-width="0.951812"/>
<path d="M316.386 1.00054L285.005 24.114C278.793 28.689 277.156 37.2775 281.247 43.8175C285.146 50.0487 293.037 52.4594 299.752 49.4711L335 33.7854L323.711 29.9424L333.439 12.5293L312.783 13.2498L316.386 1.00054Z" stroke="#00005A" stroke-width="0.951812" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M303.042 40.0151L297.887 44.4523L294.168 42.1033L289.404 45.6269L283.662 39.0364L286.011 29.705L294.494 25.1372L302.781 30.0313L300.367 34.0118L303.042 40.0151Z" stroke="#00005A" stroke-width="0.951812" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M294.167 42.1035L293.58 38.2534" stroke="#00005A" stroke-width="0.951812" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M300.366 34.0122L292.275 34.1426L288.034 31.3368" stroke="#00005A" stroke-width="0.951812" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
<defs>
<clipPath id="paint4_diamond_0_1_clip_path"><path d="M0 21.6512C9.80703 23.6943 11.4636 25.3505 13.5067 35.1582C15.5501 25.3505 17.206 23.6943 27.0137 21.6512C17.206 19.6081 15.5501 17.952 13.5067 8.14426C11.4636 17.952 9.80703 19.6081 0 21.6512Z"/></clipPath><clipPath id="paint5_diamond_0_1_clip_path"><path d="M176.063 59.8847C182.136 61.1499 183.162 62.1757 184.427 68.249C185.692 62.1757 186.718 61.1499 192.791 59.8847C186.718 58.6195 185.692 57.5939 184.427 51.5206C183.162 57.5939 182.136 58.6195 176.063 59.8847Z"/></clipPath><clipPath id="paint6_diamond_0_1_clip_path"><path d="M92.9688 117.628C107.764 120.71 110.262 123.209 113.344 138.003C116.426 123.209 118.924 120.71 133.719 117.628C118.924 114.546 116.426 112.048 113.344 97.2533C110.262 112.048 107.764 114.546 92.9688 117.628Z"/></clipPath><filter id="filter0_i_0_1" x="57.8964" y="219.775" width="35.2056" height="35.2056" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-4" dy="-4"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.192157 0 0 0 0 0.709804 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_0_1"/>
</filter>
<clipPath id="paint14_diamond_0_1_clip_path"><path d="M68.7519 175.038L68.3635 172.2L67.975 175.038C67.1728 180.901 62.5133 185.481 56.6384 186.184L54.2256 186.472L56.6384 186.761C62.5133 187.463 67.1728 192.044 67.975 197.906L68.3635 200.745L68.7519 197.906C69.5541 192.044 74.2136 187.463 80.0885 186.761L82.5013 186.472L80.0885 186.184C74.2136 185.481 69.5541 180.901 68.7519 175.038Z"/></clipPath><clipPath id="paint15_diamond_0_1_clip_path"><path d="M24.3912 151.628L23.3179 148L22.2445 151.628C21.66 153.603 20.1027 155.141 18.1198 155.7L14.7487 156.651L18.1198 157.602C20.1027 158.161 21.66 159.698 22.2445 161.674L23.3179 165.302L24.3912 161.674C24.9758 159.698 26.5331 158.161 28.516 157.602L31.8871 156.651L28.516 155.7C26.5331 155.141 24.9758 153.603 24.3912 151.628Z"/></clipPath><clipPath id="paint16_diamond_0_1_clip_path"><path d="M30.6913 220.71L30.3029 217.872L29.9145 220.71C29.1123 226.573 24.4528 231.154 18.5779 231.856L16.165 232.144L18.5779 232.433C24.4528 233.135 29.1123 237.716 29.9145 243.579L30.3029 246.417L30.6913 243.579C31.4935 237.716 36.153 233.135 42.0279 232.433L44.4408 232.144L42.0279 231.856C36.153 231.154 31.4935 226.573 30.6913 220.71Z"/></clipPath><clipPath id="paint17_diamond_0_1_clip_path"><path d="M103.945 265.431L103.557 262.593L103.168 265.431C102.366 271.293 97.7067 275.874 91.8318 276.577L89.4189 276.865L91.8318 277.154C97.7067 277.856 102.366 282.437 103.168 288.299L103.557 291.138L103.945 288.299C104.747 282.437 109.407 277.856 115.282 277.154L117.695 276.865L115.282 276.577C109.407 275.874 104.747 271.293 103.945 265.431Z"/></clipPath><linearGradient id="paint0_linear_0_1" x1="243.682" y1="240.418" x2="359" y2="240.418" gradientUnits="userSpaceOnUse">
<stop stop-color="#3FFFFF"/>
<stop offset="0.508977" stop-color="#3FC9FF"/>
<stop offset="0.786159" stop-color="#3FC9FF"/>
<stop offset="1" stop-color="#006AFF"/>
</linearGradient>
<linearGradient id="paint1_linear_0_1" x1="243.682" y1="240.487" x2="313.773" y2="240.487" gradientUnits="userSpaceOnUse">
<stop stop-color="#FF9EC9"/>
<stop offset="0.147637" stop-color="#FF9EC9"/>
<stop offset="0.481499" stop-color="#FF9EC9"/>
<stop offset="1" stop-color="#FFE700"/>
</linearGradient>
<linearGradient id="paint2_linear_0_1" x1="233.344" y1="131.31" x2="281.964" y2="131.31" gradientUnits="userSpaceOnUse">
<stop stop-color="#FF9EC9"/>
<stop offset="0.147637" stop-color="#FF9EC9"/>
<stop offset="0.481499" stop-color="#FF9EC9"/>
<stop offset="1" stop-color="#FFE700"/>
</linearGradient>
<linearGradient id="paint3_linear_0_1" x1="243.682" y1="240.418" x2="359" y2="240.418" gradientUnits="userSpaceOnUse">
<stop stop-color="#3FFFFF"/>
<stop offset="0.508977" stop-color="#3FC9FF"/>
<stop offset="0.786159" stop-color="#3FC9FF"/>
<stop offset="1" stop-color="#006AFF"/>
</linearGradient>
<linearGradient id="paint4_diamond_0_1" x1="0" y1="0" x2="500" y2="500" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFE700"/>
<stop offset="1" stop-color="#FFA5FF" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint5_diamond_0_1" x1="0" y1="0" x2="500" y2="500" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFE700"/>
<stop offset="1" stop-color="#FFA5FF" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint6_diamond_0_1" x1="0" y1="0" x2="500" y2="500" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFE700"/>
<stop offset="1" stop-color="#FFA5FF" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint7_linear_0_1" x1="109.202" y1="10.1216" x2="121.445" y2="10.1216" gradientUnits="userSpaceOnUse">
<stop stop-color="#3FFFFF"/>
<stop offset="0.508977" stop-color="#3FC9FF"/>
<stop offset="0.786159" stop-color="#3FC9FF"/>
<stop offset="1" stop-color="#006AFF"/>
</linearGradient>
<linearGradient id="paint8_linear_0_1" x1="109.202" y1="10.1967" x2="118.936" y2="10.1967" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFA5FF"/>
<stop offset="0.417324" stop-color="#FFE700"/>
<stop offset="1" stop-color="#FFE700"/>
</linearGradient>
<linearGradient id="paint9_linear_0_1" x1="70.1788" y1="54.3987" x2="105.377" y2="54.3987" gradientUnits="userSpaceOnUse">
<stop stop-color="#FF95FF"/>
<stop offset="0.390749" stop-color="#FF95FF"/>
<stop offset="1" stop-color="#5F2ABF"/>
</linearGradient>
<linearGradient id="paint10_linear_0_1" x1="70.1789" y1="54.4954" x2="93.6788" y2="54.4954" gradientUnits="userSpaceOnUse">
<stop stop-color="#3FFFFF"/>
<stop offset="0.508977" stop-color="#3FC9FF"/>
<stop offset="0.786159" stop-color="#3FC9FF"/>
<stop offset="1" stop-color="#006AFF"/>
</linearGradient>
<linearGradient id="paint11_linear_0_1" x1="63.6055" y1="231.669" x2="83.587" y2="246.893" gradientUnits="userSpaceOnUse">
<stop stop-color="#1387FF"/>
<stop offset="1" stop-color="#3FF0FF"/>
</linearGradient>
<linearGradient id="paint12_linear_0_1" x1="57.0687" y1="236.258" x2="92.7091" y2="248.056" gradientUnits="userSpaceOnUse">
<stop stop-color="#A2E270"/>
<stop offset="1" stop-color="#0B7E55"/>
</linearGradient>
<linearGradient id="paint13_linear_0_1" x1="65.8963" y1="232.533" x2="92.0626" y2="242.524" gradientUnits="userSpaceOnUse">
<stop stop-color="#A2E270"/>
<stop offset="1" stop-color="#0B7E55"/>
</linearGradient>
<linearGradient id="paint14_diamond_0_1" x1="0" y1="0" x2="500" y2="500" gradientUnits="userSpaceOnUse">
<stop stop-color="#FF77AD"/>
<stop offset="1" stop-color="#FFA5FF" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint15_diamond_0_1" x1="0" y1="0" x2="500" y2="500" gradientUnits="userSpaceOnUse">
<stop stop-color="#FF77AD"/>
<stop offset="1" stop-color="#FFA5FF" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint16_diamond_0_1" x1="0" y1="0" x2="500" y2="500" gradientUnits="userSpaceOnUse">
<stop stop-color="#FF77AD"/>
<stop offset="1" stop-color="#FFA5FF" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint17_diamond_0_1" x1="0" y1="0" x2="500" y2="500" gradientUnits="userSpaceOnUse">
<stop stop-color="#FF77AD"/>
<stop offset="1" stop-color="#FFA5FF" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint18_linear_0_1" x1="279" y1="25.8736" x2="335" y2="25.8736" gradientUnits="userSpaceOnUse">
<stop stop-color="#C44094"/>
<stop offset="0.488831" stop-color="#FF77BB"/>
<stop offset="0.632615" stop-color="#FF77BB"/>
<stop offset="1" stop-color="#FFFF00"/>
</linearGradient>
<linearGradient id="paint19_linear_0_1" x1="279" y1="25.8736" x2="335" y2="25.8736" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFA5FF"/>
<stop offset="0.417324" stop-color="#FFE700"/>
<stop offset="1" stop-color="#FFE700"/>
</linearGradient>
<linearGradient id="paint20_linear_0_1" x1="294.313" y1="42.5786" x2="299.738" y2="42.5786" gradientUnits="userSpaceOnUse">
<stop stop-color="#00FF00"/>
<stop offset="0.31175" stop-color="#00FF00"/>
<stop offset="1" stop-color="#68F3FF"/>
</linearGradient>
<linearGradient id="paint21_linear_0_1" x1="283.662" y1="37.1031" x2="291.458" y2="37.1031" gradientUnits="userSpaceOnUse">
<stop stop-color="#00FF00"/>
<stop offset="0.31175" stop-color="#00FF00"/>
<stop offset="1" stop-color="#68F3FF"/>
</linearGradient>
</defs>
</svg>
