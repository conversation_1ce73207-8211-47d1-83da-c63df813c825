{"name": "migaku-fe-test", "type": "module", "private": true, "scripts": {"build": "nuxt build", "dev": "nuxt dev --host", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "lint": "eslint .", "lint:fix": "eslint . --fix"}, "dependencies": {"@antfu/eslint-config": "^4.13.2", "@nuxt/eslint": "^1.4.1", "@nuxt/fonts": "^0.11.4", "@nuxt/image": "^1.10.0", "@nuxt/scripts": "^0.11.7", "@nuxt/test-utils": "^3.19.0", "@unhead/vue": "^2.0.9", "eslint": "^9.27.0", "nuxt": "^3.17.4", "vue": "^3.5.14", "vue-router": "^4.5.1"}, "devDependencies": {"eslint-plugin-format": "^1.0.1", "sass-embedded": "^1.89.0", "vitest": "^3.1.4"}}