import { ref } from "vue"

type Deck = {
  category: string
  tag: string
  color: string
  emoji?: string
  letter?: string
  letterColor?: string
  isPaused?: boolean
  isCompleted?: boolean
  action?: () => void
}

const isModalOpen = ref(false)
const selectedDeck = ref<Deck | null>(null)

export function useModal() {
  function openModal(deck: Deck) {
    selectedDeck.value = deck
    isModalOpen.value = true
  }

  function closeModal() {
    isModalOpen.value = false
    // Delay clearing the deck to allow for exit animation
    setTimeout(() => {
      selectedDeck.value = null
    }, 300)
  }

  return {
    isModalOpen: readonly(isModalOpen),
    selectedDeck: readonly(selectedDeck),
    openModal,
    closeModal,
  }
}
