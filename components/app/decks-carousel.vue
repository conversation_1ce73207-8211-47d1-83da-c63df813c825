<script setup lang="ts">
import { onMounted, onUnmounted, ref } from "vue"

type Deck = {
  category: string
  tag: string
  color: string
  emoji?: string
  letter?: string
  letterColor?: string
  isPaused?: boolean
  isCompleted?: boolean
  action?: () => void
}

type Props = {
  decks?: Deck[]
  scrollAmount?: number
}

const props = withDefaults(defineProps<Props>(), {
  decks: () => [],
  scrollAmount: 300,
})

// Reactive references
const carouselContainer = ref<HTMLElement | null>(null)
const isAtStart = ref<boolean>(true)
const isAtEnd = ref<boolean>(false)
const pressedDeckIndex = ref<number | null>(null)

function updateNavigationState(): void {
  if (carouselContainer.value) {
    const { scrollLeft, scrollWidth, clientWidth } = carouselContainer.value
    isAtStart.value = scrollLeft === 0
    isAtEnd.value = scrollLeft + clientWidth >= scrollWidth - 1
  }
}

// Handle press event
function handlePressed(index: number) {
  pressedDeckIndex.value = index
}

function handleReleased() {
  pressedDeckIndex.value = null
}

// Lifecycle
onMounted(() => {
  if (carouselContainer.value) {
    carouselContainer.value.addEventListener("scroll", updateNavigationState)
    updateNavigationState()
  }
})

onUnmounted(() => {
  if (carouselContainer.value) {
    carouselContainer.value.removeEventListener("scroll", updateNavigationState)
  }
})
</script>

<template>
  <div class="decks-carousel">
    <div ref="carouselContainer" class="decks-carousel__container">
      <div
        v-for="(deck, index) in decks"
        :key="index"
        class="decks-carousel__deck"
        :class="{
          'decks-carousel__deck--is-paused': deck.isPaused,
          'decks-carousel__deck--is-completed': deck.isCompleted,
          'decks-carousel__deck--is-pressed': pressedDeckIndex === index,
        }"
        @pointerdown.prevent="handlePressed(index)"
        @pointerup.prevent="handleReleased"
        @pointerleave.prevent="handleReleased"
        @pointercancel.prevent="handleReleased"
      >
        <div
          class="decks-carousel__deck-content"
        >
          <div
            class="decks-carousel__deck-illustration"
            :style="{ backgroundColor: deck.color }"
          >
            <img
              v-if="deck.emoji"
              :src="`/emojis/${deck.emoji}.svg`"
              :alt="deck.category"
              class="decks-carousel__deck-illustration--emoji"
            >
            <span
              v-if="deck.letter"
              class="decks-carousel__deck-illustration--letter"
              :style="{ color: deck.letterColor }"
            >
              {{ deck.letter }}
            </span>
          </div>
          <div class="decks-carousel__deck-body">
            <p class="decks-carousel__deck-category">
              {{ deck.category }}
            </p>
            <p class="decks-carousel__deck-tag">
              {{ deck.isPaused ? "Paused" : deck.isCompleted ? "Completed" : deck.tag }}
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.decks-carousel {
  position: relative;
  width: 100%;

  &__container {
    display: flex;
    gap: 1rem;
    overflow-x: auto;
    overflow-y: hidden;
    scroll-behavior: smooth;
    padding: 1rem 16px;
    scrollbar-width: none;
    -ms-overflow-style: none;

    &::-webkit-scrollbar {
      display: none;
    }
  }

  &__deck {
    flex: 0 0 152px;
    height: 180px;
    cursor: pointer;

    &:last-child {
      margin-right: 2rem; // Ensures last deck has some space from edge
    }

    &--is-paused {
      .decks-carousel__deck-content {
        background: rgba(255, 255, 255, 0.6);
      }
      .decks-carousel__deck-tag {
        background: #00005a12;
        color: #00005a99;
      }
    }

    &--is-completed {
      .decks-carousel__deck-tag {
        background: #ddfff9;
        color: #00c7a4;
      }

      .decks-carousel__deck-illustration {
        position: relative;

        &:after {
          content: "";
          position: absolute;
          top: 0;
          right: 0;
          width: 24px;
          height: 24px;
          background: url("/assets/icons/checkmark.svg") center no-repeat;
          background-size: 9px;
          background-color: #00c7a4;
          border-radius: 50%;
          box-shadow:
            0px 0.6px 1.33px 0px rgba(0, 0, 90, 0.008),
            0px 1.13px 2.5px 0px rgba(0, 0, 90, 0.026),
            0px 2.01px 4.47px 0px rgba(0, 0, 90, 0.05),
            0px 3.76px 8.36px 0px rgba(0, 0, 90, 0.083),
            0px 9px 20px 0px rgba(0, 0, 90, 0.14);
        }
      }
    }

    &--is-pressed {
      .decks-carousel__deck-content {
        transform: scale(0.91);
      }
    }
  }

  &__deck-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 16px;
    background: #ffffff;
    border-radius: 12px;
    box-shadow: var(--box-shadow-card);
    overflow: hidden;
    height: 100%;
    width: 100%;
    transition: transform 0.1s ease-in-out;
  }

  &__deck-illustration {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 80px;
    height: 80px;
    border-radius: 50%;
    border: 1px solid var(--border-transparent);

    &--emoji {
      width: auto;
      height: auto;
      object-fit: contain;
    }

    &--letter {
      font-family: var(--font-primary);
      font-size: 40px;
      font-weight: 900;
      font-variation-settings: "wght" 900;
      letter-spacing: -0.03em;
    }
  }

  &__deck-body {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 8px;
    width: 100%;
    padding: 0 16px;
  }

  &__deck-category {
    font-size: 16px;
    font-weight: 600;
    text-align: center;
    color: var(--color-text-primary);
    line-height: 20px;
    display: -webkit-box;
    line-clamp: 1;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  &__deck-tag {
    font-size: 12px;
    line-height: 18px;
    font-weight: 600;
    padding: 3px 8px;
    color: var(--white);
    background-color: var(--accent-4);
    border-radius: 62px;
  }
}
</style>
