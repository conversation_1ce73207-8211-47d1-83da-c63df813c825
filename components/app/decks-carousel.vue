<script setup lang="ts">
import { onMounted, onUnmounted, ref } from "vue"

import DeckCard from "~/components/app/deck-card.vue"

type Deck = {
  category: string
  tag: string
  color: string
  emoji?: string
  letter?: string
  letterColor?: string
  isPaused?: boolean
  isCompleted?: boolean
  action?: () => void
}

type Props = {
  decks?: Deck[]
  scrollAmount?: number
}

const { decks } = withDefaults(defineProps<Props>(), {
  decks: () => [],
  scrollAmount: 300,
})

// Reactive references
const carouselContainer = ref<HTMLElement | null>(null)
const isAtStart = ref<boolean>(true)
const isAtEnd = ref<boolean>(false)

function updateNavigationState(): void {
  if (carouselContainer.value) {
    const { scrollLeft, scrollWidth, clientWidth } = carouselContainer.value
    isAtStart.value = scrollLeft === 0
    isAtEnd.value = scrollLeft + clientWidth >= scrollWidth - 1
  }
}

// Lifecycle
onMounted(() => {
  if (carouselContainer.value) {
    carouselContainer.value.addEventListener("scroll", updateNavigationState)
    updateNavigationState()
  }
})

onUnmounted(() => {
  if (carouselContainer.value) {
    carouselContainer.value.removeEventListener("scroll", updateNavigationState)
  }
})
</script>

<template>
  <div class="decks-carousel">
    <div ref="carouselContainer" class="decks-carousel__container">
      <DeckCard
        v-for="(deck, index) in decks"
        :key="index"
        :deck="deck"
        class="decks-carousel__deck"
      />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.decks-carousel {
  position: relative;
  width: 100%;

  &__container {
    display: flex;
    gap: 1rem;
    overflow-x: auto;
    overflow-y: hidden;
    scroll-behavior: smooth;
    padding: 1rem 16px;
    scrollbar-width: none;
    -ms-overflow-style: none;

    &::-webkit-scrollbar {
      display: none;
    }
  }

  &__deck {
    flex: 0 0 152px;
    height: 180px;
  }
}
</style>
