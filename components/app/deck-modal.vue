<script setup lang="ts">
import { onMounted, onUnmounted, ref, watch } from "vue"

type Deck = {
  category: string
  tag: string
  color: string
  emoji?: string
  letter?: string
  letterColor?: string
  isPaused?: boolean
  isCompleted?: boolean
  action?: () => void
}

type Props = {
  isOpen: boolean
  deck: Deck | null
}

type Emits = {
  close: []
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const modalContent = ref<HTMLElement | null>(null)
const isDragging = ref(false)
const startY = ref(0)
const currentY = ref(0)
const translateY = ref(0)
const isLoading = ref(false)
const isPressed = ref(false)

function handleBackdropClick() {
  emit("close")
}

function handleEscapeKey(event: KeyboardEvent) {
  if (event.key === "Escape" && props.isOpen) {
    emit("close")
  }
}

function handlePressed() {
  isPressed.value = true
}

function handleReleased() {
  isPressed.value = false
}

function handleStudyClick() {
  if (isLoading.value)
    return // Prevent multiple clicks during loading

  isLoading.value = true

  // Simulate loading for 3 seconds
  setTimeout(() => {
    isLoading.value = false
  }, 3000)
}

// Touch/drag handlers for swipe to close
function handleTouchStart(event: TouchEvent) {
  isDragging.value = true
  startY.value = event.touches[0].clientY
  currentY.value = startY.value
}

function handleTouchMove(event: TouchEvent) {
  if (!isDragging.value)
    return

  currentY.value = event.touches[0].clientY
  const deltaY = currentY.value - startY.value

  // Only allow downward swipes
  if (deltaY > 0) {
    translateY.value = deltaY
    if (modalContent.value) {
      // Ensure no transition during drag
      modalContent.value.style.transition = "none"
      modalContent.value.style.transform = `translateY(${deltaY}px)`
    }
  }
}

function handleTouchEnd() {
  if (!isDragging.value)
    return

  const deltaY = currentY.value - startY.value
  const threshold = 100 // Close if dragged down more than 100px

  if (deltaY > threshold) {
    // Reset transform before closing to allow transition to take over
    if (modalContent.value) {
      modalContent.value.style.transform = ""
      modalContent.value.style.transition = ""
    }
    emit("close")
  }
  else {
    // Snap back to original position
    if (modalContent.value) {
      modalContent.value.style.transition = "transform 0.3s ease"
      modalContent.value.style.transform = "translateY(0)"
      // Remove inline styles after animation
      setTimeout(() => {
        if (modalContent.value) {
          modalContent.value.style.transition = ""
          modalContent.value.style.transform = ""
        }
      }, 300)
    }
  }

  isDragging.value = false
  translateY.value = 0
}

// Mouse handlers for desktop
function handleMouseDown(event: MouseEvent) {
  isDragging.value = true
  startY.value = event.clientY
  currentY.value = startY.value
}

function handleMouseMove(event: MouseEvent) {
  if (!isDragging.value)
    return

  currentY.value = event.clientY
  const deltaY = currentY.value - startY.value

  if (deltaY > 0) {
    translateY.value = deltaY
    if (modalContent.value) {
      // Ensure no transition during drag
      modalContent.value.style.transition = "none"
      modalContent.value.style.transform = `translateY(${deltaY}px)`
    }
  }
}

function handleMouseUp() {
  if (!isDragging.value)
    return

  const deltaY = currentY.value - startY.value
  const threshold = 100

  if (deltaY > threshold) {
    // Reset transform before closing to allow transition to take over
    if (modalContent.value) {
      modalContent.value.style.transform = ""
      modalContent.value.style.transition = ""
    }
    emit("close")
  }
  else {
    // Snap back to original position
    if (modalContent.value) {
      modalContent.value.style.transition = "transform 0.3s ease"
      modalContent.value.style.transform = "translateY(0)"
      // Remove inline styles after animation
      setTimeout(() => {
        if (modalContent.value) {
          modalContent.value.style.transition = ""
          modalContent.value.style.transform = ""
        }
      }, 300)
    }
  }

  isDragging.value = false
  translateY.value = 0
}

// Prevent body scroll when modal is open
watch(() => props.isOpen, (isOpen) => {
  if (isOpen) {
    document.body.style.overflow = "hidden"
  }
  else {
    document.body.style.overflow = ""
    // Reset loading state when modal closes
    isLoading.value = false
    // Reset all inline styles when modal closes to ensure clean state
    setTimeout(() => {
      if (modalContent.value) {
        modalContent.value.style.transform = ""
        modalContent.value.style.transition = ""
      }
    }, 300) // Wait for transition to complete
  }
})

onMounted(() => {
  document.addEventListener("keydown", handleEscapeKey)
  document.addEventListener("mousemove", handleMouseMove)
  document.addEventListener("mouseup", handleMouseUp)
})

onUnmounted(() => {
  document.removeEventListener("keydown", handleEscapeKey)
  document.removeEventListener("mousemove", handleMouseMove)
  document.removeEventListener("mouseup", handleMouseUp)
  // Ensure body scroll is restored
  document.body.style.overflow = ""
})
</script>

<template>
  <Teleport to="body">
    <Transition name="modal">
      <div
        v-if="isOpen"
        class="modal-overlay"
        @click="handleBackdropClick"
      >
        <div
          ref="modalContent"
          class="modal-content"
          @click.stop
          @touchstart="handleTouchStart"
          @touchmove="handleTouchMove"
          @touchend="handleTouchEnd"
          @mousedown="handleMouseDown"
        >
          <!-- Drag handle -->
          <div class="modal-handle" />

          <div v-if="deck" class="modal-body">
            <div
              class="modal-deck-illustration"
              :style="{ backgroundColor: deck.color }"
            >
              <img
                v-if="deck.emoji"
                :src="`/emojis/${deck.emoji}.svg`"
                :alt="deck.category"
                class="modal-deck-illustration--emoji"
              >
              <span
                v-if="deck.letter"
                class="modal-deck-illustration--letter"
                :style="{ color: deck.letterColor }"
              >
                {{ deck.letter }}
              </span>
            </div>

            <div>
              <p class="modal-title">
                {{ deck.category }}
              </p>
              <p class="modal-tag">
                Completed
              </p>
            </div>

            <p class="modal-subtitle">
              You learned 5 new words today
            </p>

            <div class="modal-actions">
              <button
                class="modal-button modal-button--primary"
                :class="{ 'modal-button--is-pressed': isPressed }"
                :disabled="isLoading"
                @click="handleStudyClick"
                @pointerdown.prevent="handlePressed"
                @pointerup.prevent="handleReleased"
                @pointerleave.prevent="handleReleased"
                @pointercancel.prevent="handleReleased"
              >
                <img
                  v-if="isLoading"
                  src="/assets/images/migaku.svg"
                  alt="Loading"
                  class="modal-button__loader"
                >
                <span v-else>STUDY 3 NEW CARDS</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </Transition>
  </Teleport>
</template>

<style lang="scss" scoped>
@import "~/assets/styles/mixins.scss";

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--overlay);
  display: flex;
  align-items: flex-end;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  width: 100%;
  background: var(--white);
  border-radius: var(--border-radius-modal) var(--border-radius-modal) 0 0;
  box-shadow: var(--box-shadow-modal);
  position: relative;
  transition: transform 0.2s ease-out;
  user-select: none;
}

.modal-handle {
  position: absolute;
  top: 12px;
  left: 50%;
  transform: translateX(-50%);
  width: 40px;
  height: 5px;
  background: var(--grey-3);
  border-radius: 50px;
  cursor: grab;

  &:active {
    cursor: grabbing;
  }
}

.modal-body {
  padding: 28px 28px 40px;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  gap: 16px;

  > div:has(.modal-title) {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: 8px;
  }
}

.modal-deck-illustration {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  border-radius: 50%;
  border: 1px solid var(--border-transparent);
  position: relative;
  margin-bottom: 8px;

  &:after {
    content: "";
    position: absolute;
    top: 0;
    right: 0;
    width: 24px;
    height: 24px;
    background: url("/assets/icons/checkmark.svg") center no-repeat;
    background-size: 9px;
    background-color: #00c7a4;
    border-radius: 50%;
    box-shadow:
      0px 0.6px 1.33px 0px rgba(0, 0, 90, 0.008),
      0px 1.13px 2.5px 0px rgba(0, 0, 90, 0.026),
      0px 2.01px 4.47px 0px rgba(0, 0, 90, 0.05),
      0px 3.76px 8.36px 0px rgba(0, 0, 90, 0.083),
      0px 9px 20px 0px rgba(0, 0, 90, 0.14);
  }

  &--emoji {
    width: auto;
    height: auto;
    object-fit: contain;
  }

  &--letter {
    font-family: var(--font-primary);
    font-size: 40px;
    font-weight: 900;
    font-variation-settings: "wght" 900;
    letter-spacing: -0.03em;
  }
}

.modal-title {
  font-size: 16px;
  font-weight: 600;
  text-align: center;
  color: var(--color-text-primary);
  line-height: 20px;
}

.modal-tag {
  font-size: 12px;
  line-height: 18px;
  font-weight: 600;
  padding: 3px 8px;
  background: #ddfff9;
  color: #00c7a4;
  border-radius: var(--border-radius-tag);
}

.modal-subtitle {
  font-size: 14px;
  font-weight: 500;
  text-align: center;
  color: var(--color-text-primary);
  line-height: 20px;
}

.modal-actions {
  width: 100%;
  margin-top: 8px;
}

.modal-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 40px;
  border-radius: var(--border-radius-button);
  font-size: 14px;
  cursor: pointer;
  transition: transform 0.1s ease-in-out;
  border: none;
  text-transform: uppercase;
  @include font-primary;

  &:disabled {
    cursor: not-allowed;
  }

  &__loader {
    width: 36px;
    height: 36px;
    animation: spin 1s linear infinite;
  }

  &--primary {
    background: var(--gradient-primary);
    color: var(--white);
  }

  &--is-pressed {
    transform: scale(0.91);
  }
}

// Spinning animation for the loader
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// Transition animations
.modal-enter-active {
  transition: opacity 0.2s ease;

  .modal-content {
    transition: transform 0.2s ease-out;
  }
}

.modal-leave-active {
  transition: opacity 0.2s ease;

  .modal-content {
    transition: transform 0.2s ease-in;
  }
}

.modal-enter-from {
  opacity: 0;

  .modal-content {
    transform: translateY(100%);
  }
}

.modal-leave-to {
  opacity: 0;

  .modal-content {
    transform: translateY(100%);
  }
}
</style>
