<script setup lang="ts">
import { onMounted, onUnmounted, watch } from "vue"

type Deck = {
  category: string
  tag: string
  color: string
  emoji?: string
  letter?: string
  letterColor?: string
  isPaused?: boolean
  isCompleted?: boolean
  action?: () => void
}

type Props = {
  isOpen: boolean
  deck: Deck | null
}

type Emits = {
  close: []
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

function handleBackdropClick() {
  emit("close")
}

function handleEscapeKey(event: KeyboardEvent) {
  if (event.key === "Escape" && props.isOpen) {
    emit("close")
  }
}

// Prevent body scroll when modal is open
watch(() => props.isOpen, (isOpen) => {
  if (isOpen) {
    document.body.style.overflow = "hidden"
  } else {
    document.body.style.overflow = ""
  }
})

onMounted(() => {
  document.addEventListener("keydown", handleEscapeKey)
})

onUnmounted(() => {
  document.removeEventListener("keydown", handleEscapeKey)
  // Ensure body scroll is restored
  document.body.style.overflow = ""
})
</script>

<template>
  <Teleport to="body">
    <Transition name="modal">
      <div
        v-if="isOpen"
        class="modal-overlay"
        @click="handleBackdropClick"
      >
        <div
          class="modal-content"
          @click.stop
        >
          <div class="modal-header">
            <button
              class="modal-close"
              @click="emit('close')"
            >
              ×
            </button>
          </div>
          
          <div v-if="deck" class="modal-body">
            <div
              class="modal-deck-illustration"
              :style="{ backgroundColor: deck.color }"
            >
              <img
                v-if="deck.emoji"
                :src="`/emojis/${deck.emoji}.svg`"
                :alt="deck.category"
                class="modal-deck-illustration--emoji"
              >
              <span
                v-if="deck.letter"
                class="modal-deck-illustration--letter"
                :style="{ color: deck.letterColor }"
              >
                {{ deck.letter }}
              </span>
            </div>
            
            <h2 class="modal-title">{{ deck.category }}</h2>
            <p class="modal-tag">{{ deck.tag }}</p>
            
            <div class="modal-actions">
              <button class="modal-button modal-button--primary">
                Review Deck
              </button>
              <button class="modal-button modal-button--secondary">
                View Stats
              </button>
            </div>
          </div>
        </div>
      </div>
    </Transition>
  </Teleport>
</template>

<style lang="scss" scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: flex-end;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 20px 20px 0 0;
  width: 100%;
  max-width: 500px;
  max-height: 80vh;
  overflow-y: auto;
  position: relative;
}

.modal-header {
  display: flex;
  justify-content: flex-end;
  padding: 16px 20px 0;
}

.modal-close {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s;

  &:hover {
    background-color: #f0f0f0;
  }
}

.modal-body {
  padding: 0 32px 32px;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  gap: 20px;
}

.modal-deck-illustration {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 120px;
  height: 120px;
  border-radius: 50%;
  border: 1px solid var(--border-transparent);
  position: relative;

  &--emoji {
    width: auto;
    height: auto;
    object-fit: contain;
  }

  &--letter {
    font-family: var(--font-primary);
    font-size: 60px;
    font-weight: 900;
    font-variation-settings: "wght" 900;
    letter-spacing: -0.03em;
  }

  &:after {
    content: "";
    position: absolute;
    top: -4px;
    right: -4px;
    width: 32px;
    height: 32px;
    background: url("/assets/icons/checkmark.svg") center no-repeat;
    background-size: 12px;
    background-color: #00c7a4;
    border-radius: 50%;
    box-shadow: var(--box-shadow-card);
  }
}

.modal-title {
  font-family: var(--font-primary);
  font-size: 24px;
  font-weight: 700;
  color: var(--color-text-primary);
  margin: 0;
}

.modal-tag {
  font-size: 14px;
  font-weight: 600;
  padding: 6px 12px;
  color: #00c7a4;
  background-color: #ddfff9;
  border-radius: 62px;
  margin: 0;
}

.modal-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
  width: 100%;
  margin-top: 8px;
}

.modal-button {
  padding: 16px 24px;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  border: none;

  &--primary {
    background: var(--accent-4);
    color: white;

    &:hover {
      background: #2980ff;
    }
  }

  &--secondary {
    background: #f8f9fa;
    color: var(--color-text-primary);
    border: 1px solid var(--border-transparent);

    &:hover {
      background: #e9ecef;
    }
  }
}

// Transition animations
.modal-enter-active,
.modal-leave-active {
  transition: all 0.3s ease;
}

.modal-enter-from,
.modal-leave-to {
  opacity: 0;
}

.modal-enter-from .modal-content,
.modal-leave-to .modal-content {
  transform: translateY(100%);
}

.modal-enter-active .modal-content,
.modal-leave-active .modal-content {
  transition: transform 0.3s ease;
}
</style>
