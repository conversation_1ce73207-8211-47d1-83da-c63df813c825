<template>
  <nav class="nav-bar">
    <ul class="nav-bar__list">
      <li class="nav-bar__item nav-bar__item--active" data-icon="book">
        <a href="#">
          <img src="/assets/icons/book.svg" alt="Home">
        </a>
      </li>
      <li class="nav-bar__item" data-icon="book-stack">
        <img src="/assets/icons/book-stack.svg" alt="Decks">
      </li>
    </ul>
  </nav>
</template>

<style scoped lang="scss">
.nav-bar {
  position: fixed;
  bottom: 40px;
  left: 50%;
  transform: translateX(-50%);
  width: auto;
  height: 56px;
  background: var(--white);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16px 24px;
  border: 1px solid var(--border-nav-bar);
  border-radius: var(--border-radius-button);
  box-shadow: var(--box-shadow-nav-bar);
  z-index: 10;

  &__list {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 32px;
    list-style: none;
    padding: 0;
    margin: 0;
  }

  &__item {
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;

    img {
      width: 24px;
      height: 24px;
      opacity: 0.25;
      transition: all 0.2s ease-in-out;
    }

    &--active {
      position: relative;

      img {
        opacity: 0; // Hide the original image
      }

      // Create a gradient-filled version using ::before
      &::before {
        content: "";
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 24px;
        height: 24px;
        background: var(--gradient-primary);
        mask-size: contain;
        -webkit-mask-size: contain;
      }

      // Specific icons using data attributes
      &[data-icon="book"]::before {
        mask: url("/assets/icons/book.svg") no-repeat center;
        -webkit-mask: url("/assets/icons/book.svg") no-repeat center;
      }

      &[data-icon="book-stack"]::before {
        mask: url("/assets/icons/book-stack.svg") no-repeat center;
        -webkit-mask: url("/assets/icons/book-stack.svg") no-repeat center;
      }
    }
  }
}
</style>
