<template>
  <nav class="nav-bar">
    <ul class="nav-bar__list">
      <li class="nav-bar__item">
        <a href="#">
          <img src="/assets/icons/book.svg" alt="Home">
        </a>
      </li>
      <li>
        <img src="/assets/icons/book-stack.svg" alt="Decks">
      </li>
    </ul>
  </nav>
</template>

<style scoped lang="scss">
.nav-bar {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 56px;
  background: var(--white);
  border-bottom: 1px solid var(--grey-2);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;

  &__list {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 24px;
    list-style: none;
    padding: 0;
    margin: 0;
  }

  &__item {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease-in-out;

    &:hover {
      transform: scale(1.1);
    }
  }
}
</style>
