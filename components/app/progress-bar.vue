<script setup lang="ts">
import { ref } from "vue"

type Props = {
  progress?: number
}

const props = withDefaults(defineProps<Props>(), {
  progress: 0,
})

// Internal reactive state for progress
const currentProgress = ref(props.progress)
const isAnimating = ref(false)

// Toggle progress between 0% and 100%
function toggleProgress() {
  if (isAnimating.value)
    return // Prevent clicks during animation

  isAnimating.value = true

  if (currentProgress.value === 0) {
    // Fill to 100%
    currentProgress.value = 100
  }
  else {
    // Reset to 0%
    currentProgress.value = 0
  }

  // Reset animation flag after animation completes
  setTimeout(() => {
    isAnimating.value = false
  }, 300) // Match animation duration
}
</script>

<template>
  <section class="progress-bar" @click="toggleProgress">
    <div class="progress-bar__container">
      <div class="progress-bar__track">
        <div
          class="progress-bar__fill"
          :style="{ width: `${currentProgress}%` }"
        />
      </div>
    </div>
    <Transition name="fade" mode="out-in">
      <div :key="currentProgress" class="progress-bar__value">
        {{ currentProgress }}%
      </div>
    </Transition>
  </section>
</template>

<style scoped lang="scss">
.progress-bar {
  display: grid;
  grid-template-columns: 1fr 44px;
  grid-gap: 8px;
  align-items: center;
  justify-content: center;
  position: fixed;
  top: 51px;
  left: 50%;
  transform: translateX(-50%);
  width: 180px;
  height: 40px;
  padding: 0 16px;
  background: var(--white);
  border-radius: var(--border-radius-button);
  box-shadow: var(--box-shadow-card);
  z-index: 10;
  cursor: pointer;
  transition: transform 0.1s ease;

  // iOS Safari optimizations
  -webkit-transform: translateX(-50%);
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  -webkit-perspective: 1000px;
  perspective: 1000px;
  transform-style: preserve-3d;

  &:active {
    transform: translateX(-50%) translateY(-1px);
  }

  &__container {
    width: 100%;
    height: 24px;
    display: flex;
    align-items: center;
  }

  &__track {
    width: 100%;
    height: 12px;
    position: relative;
    overflow: hidden;
    background: rgba(0, 0, 90, 0.1);

    // Create zigzag edges using CSS borders - iOS safe approach
    &::before {
      content: "";
      position: absolute;
      top: -1px;
      left: 0;
      right: 0;
      height: 2px;
      background: linear-gradient(
        90deg,
        transparent 0px,
        transparent 4px,
        rgba(0, 0, 90, 0.1) 4px,
        rgba(0, 0, 90, 0.1) 8px,
        transparent 8px,
        transparent 12px,
        rgba(0, 0, 90, 0.1) 12px,
        rgba(0, 0, 90, 0.1) 16px
      );
      background-size: 16px 2px;
      background-repeat: repeat-x;
    }

    &::after {
      content: "";
      position: absolute;
      bottom: -1px;
      left: 0;
      right: 0;
      height: 2px;
      background: linear-gradient(
        90deg,
        rgba(0, 0, 90, 0.1) 0px,
        rgba(0, 0, 90, 0.1) 4px,
        transparent 4px,
        transparent 8px,
        rgba(0, 0, 90, 0.1) 8px,
        rgba(0, 0, 90, 0.1) 12px,
        transparent 12px,
        transparent 16px
      );
      background-size: 16px 2px;
      background-repeat: repeat-x;
    }
  }

  &__fill {
    height: 100%;
    background: var(--gradient-primary);
    transition: width 1s cubic-bezier(0.4, 0, 0.2, 1);
    width: 0%;
    position: relative;
    z-index: 2;

    // Create matching zigzag edges for the fill
    &::before {
      content: "";
      position: absolute;
      top: -1px;
      left: 0;
      right: 0;
      height: 2px;
      background: linear-gradient(
        90deg,
        transparent 0px,
        transparent 4px,
        var(--gradient-primary) 4px,
        var(--gradient-primary) 8px,
        transparent 8px,
        transparent 12px,
        var(--gradient-primary) 12px,
        var(--gradient-primary) 16px
      );
      background-size: 16px 2px;
      background-repeat: repeat-x;
    }

    &::after {
      content: "";
      position: absolute;
      bottom: -1px;
      left: 0;
      right: 0;
      height: 2px;
      background: linear-gradient(
        90deg,
        var(--gradient-primary) 0px,
        var(--gradient-primary) 4px,
        transparent 4px,
        transparent 8px,
        var(--gradient-primary) 8px,
        var(--gradient-primary) 12px,
        transparent 12px,
        transparent 16px
      );
      background-size: 16px 2px;
      background-repeat: repeat-x;
    }
  }

  &__value {
    text-align: center;
    font-size: 16px;
    font-weight: 700;
    line-height: 20px;
    color: var(--color-text-primary);
  }
}

// Fade transition for value changes
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.2s ease-in-out;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.fade-enter-to,
.fade-leave-from {
  opacity: 1;
}

// iOS-specific optimizations
@supports (-webkit-touch-callout: none) {
  .progress-bar {
    // Force hardware acceleration on iOS
    -webkit-transform: translate3d(-50%, 0, 0);
    transform: translate3d(-50%, 0, 0);
  }
}
</style>
