<script setup lang="ts">
import { ref } from "vue"

type Props = {
  progress?: number
}

const props = withDefaults(defineProps<Props>(), {
  progress: 0,
})

// Internal reactive state for progress
const currentProgress = ref(props.progress)
const isAnimating = ref(false)

// Toggle progress between 0% and 100%
function toggleProgress() {
  if (isAnimating.value)
    return // Prevent clicks during animation

  isAnimating.value = true

  if (currentProgress.value === 0) {
    // Fill to 100%
    currentProgress.value = 100
  }
  else {
    // Reset to 0%
    currentProgress.value = 0
  }

  // Reset animation flag after animation completes
  setTimeout(() => {
    isAnimating.value = false
  }, 300) // Match animation duration
}
</script>

<template>
  <section class="progress-bar" @click="toggleProgress">
    <div class="progress-bar__svg-container">
      <img
        src="/assets/images/progress-bar.svg"
        alt="Progress Bar"
        class="progress-bar__svg"
      >
      <div
        class="progress-bar__fill"
        :class="{ 'progress-bar__fill--active': currentProgress > 0 }"
        :style="{ '--progress': `${currentProgress}%` }"
      />
    </div>
    <Transition name="fade" mode="out-in">
      <div :key="currentProgress" class="progress-bar__value">
        {{ currentProgress }}%
      </div>
    </Transition>
  </section>
</template>

<style scoped lang="scss">
.progress-bar {
  display: grid;
  grid-template-columns: 1fr 44px;
  grid-gap: 8px;
  align-items: center;
  justify-content: center;
  position: fixed;
  top: 51px;
  left: 50%;
  transform: translate3d(-50%, 0, 0);
  will-change: transform;
  image-rendering: crisp-edges;
  shape-rendering: geometricPrecision;
  width: 180px;
  height: 40px;
  padding: 0 16px;
  background: var(--white);
  border-radius: var(--border-radius-button);
  box-shadow: var(--box-shadow-card);
  z-index: 10;
  cursor: pointer;
  transition: transform 0.1s ease;

  &:active {
    transform: translate3d(-50%, 1px, 0);
  }

  &__svg-container {
    position: relative;
    width: 100%;
    height: 24px;
    display: flex;
    align-items: center;
  }

  &__svg {
    width: 100%;
    height: auto;
    position: relative;
    z-index: 2;
  }

  &__fill {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
    mask: url("/assets/images/progress-bar.svg") no-repeat center;
    mask-size: contain;
    -webkit-mask: url("/assets/images/progress-bar.svg") no-repeat center;
    -webkit-mask-size: contain;
    z-index: 11;

    // Initial state - no fill
    clip-path: inset(0 100% 0 0);
    transition: clip-path 0.3s ease-out;

    &--active {
      // Animate from left to right based on progress
      clip-path: inset(0 calc(100% - var(--progress)) 0 0);
    }
  }

  &__value {
    text-align: center;
    font-size: 16px;
    font-weight: 700;
    line-height: 20px;
    color: var(--color-text-primary);
  }
}

// Fade transition for value changes
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.2s ease-in-out;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.fade-enter-to,
.fade-leave-from {
  opacity: 1;
}
</style>
