<script setup lang="ts">
import { ref } from "vue"

type Props = {
  progress?: number
}

const props = withDefaults(defineProps<Props>(), {
  progress: 0,
})

// Internal reactive state for progress
const currentProgress = ref(props.progress)
const isAnimating = ref(false)

// Toggle progress between 0% and 100%
function toggleProgress() {
  if (isAnimating.value)
    return // Prevent clicks during animation

  isAnimating.value = true

  if (currentProgress.value === 0) {
    // Fill to 100%
    currentProgress.value = 100
  }
  else {
    // Reset to 0%
    currentProgress.value = 0
  }

  // Reset animation flag after animation completes
  setTimeout(() => {
    isAnimating.value = false
  }, 300) // Match animation duration
}
</script>

<template>
  <section class="progress-bar" @click="toggleProgress">
    <div class="progress-bar__svg-container">
      <img
        src="/assets/images/progress-bar.svg"
        alt="Progress Bar"
        class="progress-bar__svg"
        draggable="false"
      >
      <div
        class="progress-bar__fill"
        :class="{ 'progress-bar__fill--active': currentProgress > 0 }"
        :style="{ '--progress': `${currentProgress}%` }"
      />
    </div>
    <Transition name="fade" mode="out-in">
      <div :key="currentProgress" class="progress-bar__value">
        {{ currentProgress }}%
      </div>
    </Transition>
  </section>
</template>

<style scoped lang="scss">
.progress-bar {
  display: grid;
  grid-template-columns: 1fr 44px;
  grid-gap: 8px;
  align-items: center;
  justify-content: center;
  position: fixed;
  top: 51px;
  left: 50%;
  transform: translateX(-50%);
  width: 180px;
  height: 40px;
  padding: 0 16px;
  background: var(--white);
  border-radius: var(--border-radius-button);
  box-shadow: var(--box-shadow-card);
  z-index: 10;
  cursor: pointer;
  transition: transform 0.1s ease;

  // iOS Safari optimizations
  -webkit-transform: translateX(-50%);
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  -webkit-perspective: 1000px;
  perspective: 1000px;
  transform-style: preserve-3d;

  &:active {
    transform: translateX(-50%) translateY(-1px);
  }

  &__svg-container {
    position: relative;
    width: 100%;
    height: 24px;
    display: flex;
    align-items: center;

    // iOS Safari optimizations for SVG container
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
  }

  &__svg {
    width: 100%;
    height: auto;
    position: relative;
    z-index: 2;

    // iOS Safari SVG optimizations
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
    shape-rendering: geometricPrecision;
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
    image-rendering: pixelated;

    // Prevent user interactions that might cause rendering issues
    pointer-events: none;
    user-select: none;
    -webkit-user-select: none;
    -webkit-touch-callout: none;
    -webkit-user-drag: none;
  }

  &__fill {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
    mask: url("/assets/images/progress-bar.svg") no-repeat center;
    mask-size: contain;
    -webkit-mask: url("/assets/images/progress-bar.svg") no-repeat center;
    -webkit-mask-size: contain;
    z-index: 11;

    // iOS Safari optimizations - use transform instead of clip-path
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    -webkit-transform: translateZ(0) scaleX(0);
    transform: translateZ(0) scaleX(0);
    transform-origin: left center;
    will-change: transform;

    // Initial state - no fill using scaleX
    transition: transform 1s cubic-bezier(0.4, 0, 0.2, 1);

    &--active {
      // Animate from left to right using scaleX
      -webkit-transform: translateZ(0) scaleX(calc(var(--progress) / 100));
      transform: translateZ(0) scaleX(calc(var(--progress) / 100));
    }
  }

  &__value {
    text-align: center;
    font-size: 16px;
    font-weight: 700;
    line-height: 20px;
    color: var(--color-text-primary);
  }
}

// Fade transition for value changes
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.2s ease-in-out;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.fade-enter-to,
.fade-leave-from {
  opacity: 1;
}

// iOS-specific optimizations
@supports (-webkit-touch-callout: none) {
  .progress-bar {
    // Force hardware acceleration on iOS
    -webkit-transform: translate3d(-50%, 0, 0);
    transform: translate3d(-50%, 0, 0);

    &__svg {
      // Better SVG rendering on iOS
      shape-rendering: geometricPrecision;
      image-rendering: -webkit-optimize-contrast;
    }

    // Now using scaleX transform instead of clip-path for better iOS compatibility
  }
}
</style>
