<script setup lang="ts">
import { ref } from "vue"

const isPressed = ref(false)

function handlePressed() {
  isPressed.value = true
}

function handleReleased() {
  isPressed.value = false
}
</script>

<template>
  <button
    class="heart-button"
    :class="{ 'heart-button--is-pressed': isPressed }"
    @pointerdown.prevent="handlePressed"
    @pointerup.prevent="handleReleased"
    @pointerleave.prevent="handleReleased"
    @pointercancel.prevent="handleReleased"
  />
</template>

<style scoped lang="scss">
.heart-button {
  width: 157px;
  height: 145px;
  margin-top: 16px;
  background: url("/assets/images/back-heart-button.svg") no-repeat;
  background-size: contain;
  background-position: center;
  cursor: pointer;
  position: relative;
  transition: all 0.2s ease-in-out;

  &:after {
    content: "";
    display: flex;
    position: absolute;
    top: -16px;
    right: -14px;
    z-index: 2;
    width: 157px;
    height: 145px;
    background: url("/assets/images/front-heart-button.svg") no-repeat;
    background-size: contain;
    background-position: center;
    transition: all 0.4s ease-out;
  }

  &--is-pressed {
    background: url("/assets/images/front-heart-button.svg") no-repeat;
    background-size: contain;
    background-position: center;

    &:after {
      transform: translateX(-14px) translateY(16px);
    }
  }
}
</style>
