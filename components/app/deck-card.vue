<script setup lang="ts">
import { ref } from "vue"

type Deck = {
  category: string
  tag: string
  color: string
  emoji?: string
  letter?: string
  letterColor?: string
  isPaused?: boolean
  isCompleted?: boolean
  action?: () => void
}

type Props = {
  deck: Deck
}

const { deck } = defineProps<Props>()

// Reactive references
const isPressed = ref<boolean>(false)

// Handle press event
function handlePressed() {
  isPressed.value = true
}

function handleReleased() {
  isPressed.value = false
}

function handleClick() {
  if (deck.action) {
    deck.action()
  }
}
</script>

<template>
  <div
    class="deck-card"
    :class="{
      'deck-card--is-paused': deck.isPaused,
      'deck-card--is-completed': deck.isCompleted,
      'deck-card--is-pressed': isPressed,
    }"
    @pointerdown.prevent="handlePressed"
    @pointerup.prevent="handleReleased"
    @pointerleave.prevent="handleReleased"
    @pointercancel.prevent="handleReleased"
    @click="handleClick"
  >
    <div class="deck-card__content">
      <div
        class="deck-card__illustration"
        :style="{ backgroundColor: deck.color }"
      >
        <img
          v-if="deck.emoji"
          :src="`/emojis/${deck.emoji}.svg`"
          :alt="deck.category"
          class="deck-card__illustration--emoji"
        >
        <span
          v-if="deck.letter"
          class="deck-card__illustration--letter"
          :style="{ color: deck.letterColor }"
        >
          {{ deck.letter }}
        </span>
      </div>
      <div class="deck-card__body">
        <p class="deck-card__category">
          {{ deck.category }}
        </p>
        <p class="deck-card__tag">
          {{ deck.isPaused ? "Paused" : deck.isCompleted ? "Completed" : deck.tag }}
        </p>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.deck-card {
  cursor: pointer;
  height: 180px;
  width: 100%;

  &--is-paused {
    .deck-card__content {
      background: rgba(255, 255, 255, 0.6);
    }
    .deck-card__tag {
      background: #00005a12;
      color: #00005a99;
    }
  }

  &--is-completed {
    .deck-card__tag {
      background: #ddfff9;
      color: #00c7a4;
    }

    .deck-card__illustration {
      position: relative;

      &:after {
        content: "";
        position: absolute;
        top: 0;
        right: 0;
        width: 24px;
        height: 24px;
        background: url("/assets/icons/checkmark.svg") center no-repeat;
        background-size: 9px;
        background-color: #00c7a4;
        border-radius: 50%;
        box-shadow:
          0px 0.6px 1.33px 0px rgba(0, 0, 90, 0.008),
          0px 1.13px 2.5px 0px rgba(0, 0, 90, 0.026),
          0px 2.01px 4.47px 0px rgba(0, 0, 90, 0.05),
          0px 3.76px 8.36px 0px rgba(0, 0, 90, 0.083),
          0px 9px 20px 0px rgba(0, 0, 90, 0.14);
      }
    }
  }

  &--is-pressed {
    .deck-card__content {
      transform: scale(0.91);
    }
  }

  &__content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 16px;
    background: #ffffff;
    border-radius: 12px;
    box-shadow: var(--box-shadow-card);
    overflow: hidden;
    height: 100%;
    width: 100%;
    transition: transform 0.1s ease-in-out;
  }

  &__illustration {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 80px;
    height: 80px;
    border-radius: 50%;
    border: 1px solid var(--border-transparent);

    &--emoji {
      width: auto;
      height: auto;
      object-fit: contain;
    }

    &--letter {
      font-family: var(--font-primary);
      font-size: 40px;
      font-weight: 900;
      font-variation-settings: "wght" 900;
      letter-spacing: -0.03em;
    }
  }

  &__body {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 8px;
    width: 100%;
    padding: 0 16px;
  }

  &__category {
    font-size: 16px;
    font-weight: 700;
    text-align: center;
    color: var(--color-text-primary);
    line-height: 20px;
    display: -webkit-box;
    line-clamp: 1;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    word-break: break-all;
    overflow: hidden;
  }

  &__tag {
    font-size: 12px;
    line-height: 18px;
    font-weight: 700;
    padding: 3px 8px;
    color: var(--white);
    background-color: var(--accent-4);
    border-radius: 62px;
  }
}
</style>
