<template>
  <button class="profile-button">
    <img
      src="/assets/images/avatar.svg"
      alt="Profile"
      class="profile-button__avatar"
    >
  </button>
</template>

<style scoped lang="scss">
.profile-button {
  position: fixed;
  top: 51px;
  left: 24px;
  width: 40px;
  height: 40px;
  border-radius: var(--border-radius-rounded);
  box-shadow: var(--box-shadow-card);
  cursor: pointer;
  transition: transform 0.1s ease;

  &:active {
    transform: translateY(1px);
  }

  &__avatar {
    width: 100%;
    height: 100%;
  }
}
</style>
