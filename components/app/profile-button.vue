<script setup lang="ts">
import { ref } from "vue"

const isLoading = ref(false)

function handleClick() {
  if (isLoading.value)
    return // Prevent multiple clicks during loading

  isLoading.value = true

  // Show loading for 3 seconds
  setTimeout(() => {
    isLoading.value = false
  }, 3000)
}
</script>

<template>
  <div>
    <button class="profile-button" @click="handleClick">
      <img
        src="/assets/images/avatar.svg"
        alt="Profile"
        class="profile-button__avatar"
      >
    </button>

    <!-- Loading Overlay -->
    <Teleport to="body">
      <Transition name="loading-overlay">
        <div v-if="isLoading" class="loading-overlay">
          <div class="loading-container">
            <!-- Outer loading text - spins left to right -->
            <img
              src="/assets/images/loading.svg"
              alt="Loading"
              class="loading-text"
            >
            <!-- Inner Migaku logo - spins right to left -->
            <img
              src="/assets/images/migaku.svg"
              alt="Migaku"
              class="loading-logo"
            >
          </div>
        </div>
      </Transition>
    </Teleport>
  </div>
</template>

<style scoped lang="scss">
.profile-button {
  position: fixed;
  top: 51px;
  left: 24px;
  width: 40px;
  height: 40px;
  border-radius: var(--border-radius-rounded);
  box-shadow: var(--box-shadow-card);
  cursor: pointer;
  transition: transform 0.1s ease;

  &:active {
    transform: translateY(1px);
  }

  &__avatar {
    width: 100%;
    height: 100%;
  }
}

// Loading overlay styles
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: var(--gradient-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-text {
  width: 230px;
  height: 230px;
  animation: spin-clockwise 2s linear infinite;
}

.loading-logo {
  position: absolute;
  width: 72px;
  height: 72px;
  animation: spin-counter-clockwise 2s linear infinite;
}

// Animations
@keyframes spin-clockwise {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes spin-counter-clockwise {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(-360deg);
  }
}

// Transition animations for overlay
.loading-overlay-enter-active,
.loading-overlay-leave-active {
  transition: opacity 0.3s ease;
}

.loading-overlay-enter-from,
.loading-overlay-leave-to {
  opacity: 0;
}
</style>
